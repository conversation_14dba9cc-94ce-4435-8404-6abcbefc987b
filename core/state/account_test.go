package state

import (
	"math/big"
	"testing"

	"github.com/unicornultrafoundation/go-u2u/common"
)

func TestAccountCmp(t *testing.T) {
	// Test data
	hash1 := common.HexToHash("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef")
	hash2 := common.HexToHash("0x2234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef")
	codeHash1 := []byte{0x01, 0x02, 0x03}
	codeHash2 := []byte{0x01, 0x02, 0x04}
	balance1 := big.NewInt(100)
	balance2 := big.NewInt(200)

	tests := []struct {
		name     string
		a        *Account
		b        *Account
		expected int
	}{
		{
			name:     "both nil",
			a:        nil,
			b:        nil,
			expected: 0,
		},
		{
			name:     "a nil, b not nil",
			a:        nil,
			b:        &Account{Nonce: 1},
			expected: -1,
		},
		{
			name:     "a not nil, b nil",
			a:        &Account{Nonce: 1},
			b:        nil,
			expected: 1,
		},
		{
			name: "identical accounts",
			a: &Account{
				Nonce:    1,
				Balance:  big.NewInt(100),
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  big.NewInt(100),
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 0,
		},
		{
			name: "different nonce - a < b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    2,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: -1,
		},
		{
			name: "different nonce - a > b",
			a: &Account{
				Nonce:    2,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 1,
		},
		{
			name: "different balance - a < b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance2,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: -1,
		},
		{
			name: "different balance - a > b",
			a: &Account{
				Nonce:    1,
				Balance:  balance2,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 1,
		},
		{
			name: "nil balance in a",
			a: &Account{
				Nonce:    1,
				Balance:  nil,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: -1,
		},
		{
			name: "nil balance in b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  nil,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 1,
		},
		{
			name: "both balances nil",
			a: &Account{
				Nonce:    1,
				Balance:  nil,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  nil,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 0,
		},
		{
			name: "different root hash - a < b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash2,
				CodeHash: codeHash1,
			},
			expected: -1,
		},
		{
			name: "different root hash - a > b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash2,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 1,
		},
		{
			name: "different code hash - a < b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash2,
			},
			expected: -1,
		},
		{
			name: "different code hash - a > b",
			a: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash2,
			},
			b: &Account{
				Nonce:    1,
				Balance:  balance1,
				Root:     hash1,
				CodeHash: codeHash1,
			},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.a.Cmp(tt.b)
			if result != tt.expected {
				t.Errorf("Account.Cmp() = %d, expected %d", result, tt.expected)
			}
		})
	}
}
