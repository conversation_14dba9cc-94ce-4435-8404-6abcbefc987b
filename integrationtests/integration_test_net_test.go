//go:build !windows
// +build !windows

package integrationtests

import (
	"context"
	"math/big"
	"testing"

	"golang.org/x/net/nettest"

	"github.com/unicornultrafoundation/go-u2u/common"
	"github.com/unicornultrafoundation/go-u2u/core/types"
	"github.com/unicornultrafoundation/go-u2u/integrationtests/contracts/counter"
)

func TestIntegrationTestNet_CanStartAndStopIntegrationTestNet(t *testing.T) {
	dataDir, err := nettest.LocalPath()
	if err != nil {
		t.Fatalf(err.Error())
	}
	net, err := StartIntegrationTestNet(dataDir, false)
	if err != nil {
		t.Fatalf("Failed to start the fake network: %v", err)
	}
	net.Stop()
}
func TestIntegrationTestNet_CanStartMultipleConsecutiveInstances(t *testing.T) {
	for i := 0; i < 2; i++ {
		dataDir, err := nettest.LocalPath()
		if err != nil {
			t.Fatalf(err.Error())
		}
		net, err := StartIntegrationTestNet(dataDir, false)
		if err != nil {
			t.Fatalf("Failed to start the fake network: %v", err)
		}
		net.Stop()
	}
}
func TestIntegrationTestNet_CanFetchInformationFromTheNetwork(t *testing.T) {
	dataDir, err := nettest.LocalPath()
	if err != nil {
		t.Fatalf(err.Error())
	}
	net, err := StartIntegrationTestNet(dataDir, false)
	if err != nil {
		t.Fatalf("Failed to start the fake network: %v", err)
	}
	defer net.Stop()
	client, err := net.GetClient()
	if err != nil {
		t.Fatalf("Failed to connect to the integration test network: %v", err)
	}
	defer client.Close()
	block, err := client.BlockNumber(context.Background())
	if err != nil {
		t.Fatalf("Failed to get block number: %v", err)
	}
	if block == 0 || block > 1000 {
		t.Errorf("Unexpected block number: %v", block)
	}
}
func TestIntegrationTestNet_CanEndowAccountsWithTokens(t *testing.T) {
	dataDir, err := nettest.LocalPath()
	if err != nil {
		t.Fatalf(err.Error())
	}
	net, err := StartIntegrationTestNet(dataDir, false)
	if err != nil {
		t.Fatalf("Failed to start the fake network: %v", err)
	}
	defer net.Stop()
	client, err := net.GetClient()
	if err != nil {
		t.Fatalf("Failed to connect to the integration test network: %v", err)
	}
	address := common.Address{0x01}
	balance, err := client.BalanceAt(context.Background(), address, nil)
	if err != nil {
		t.Fatalf("Failed to get balance for account: %v", err)
	}
	increment := big.NewInt(1000)
	for i := 0; i < 10; i++ {
		if err := net.EndowAccount(address, increment); err != nil {
			t.Fatalf("Failed to endow account 1: %v", err)
		}
		want := balance.Add(balance, increment)
		balance, err = client.BalanceAt(context.Background(), address, nil)
		if err != nil {
			t.Fatalf("Failed to get balance for account: %v", err)
		}
		if want, got := want, balance; want.Cmp(got) != 0 {
			t.Fatalf("Unexpected balance for account, got %v, wanted %v", got, want)
		}
		balance = want
	}
}
func TestIntegrationTestNet_CanDeployContracts(t *testing.T) {
	dataDir, err := nettest.LocalPath()
	if err != nil {
		t.Fatalf(err.Error())
	}
	net, err := StartIntegrationTestNet(dataDir, false)
	if err != nil {
		t.Fatalf("Failed to start the fake network: %v", err)
	}
	defer net.Stop()
	_, receipt, err := DeployContract(net, counter.DeployCounter)
	if err != nil {
		t.Fatalf("Failed to deploy contract: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		t.Errorf("Contract deployment failed: %+v", receipt)
	}
}
func TestIntegrationTestNet_CanInteractWithContract(t *testing.T) {
	dataDir, err := nettest.LocalPath()
	if err != nil {
		t.Fatalf(err.Error())
	}
	net, err := StartIntegrationTestNet(dataDir, false)
	if err != nil {
		t.Fatalf("Failed to start the fake network: %v", err)
	}
	defer net.Stop()
	contract, _, err := DeployContract(net, counter.DeployCounter)
	if err != nil {
		t.Fatalf("Failed to deploy contract: %v", err)
	}
	receipt, err := net.Apply(contract.IncrementCounter)
	if err != nil {
		t.Fatalf("Failed to send transaction: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		t.Errorf("Contract deployment failed: %v", receipt)
	}
}
