version: '3'
services:
  node:
    image: u2u:latest
    container_name: u2u
    restart: always
    volumes:
      - ./data:/root/.u2u
      - ../../testnet.g:/testnet.g
      - /etc/password:/password
    ports:
      - 30303:30303
      - 30303:30303/udp
      - 19090:19090
    logging:
      driver: "json-file"
      options:
        max-size: 300m
    command:
      --genesis /testnet.g
      --gcmode full
      --genesis.allowExperimental
      --cache 3875
      --port 30303
      --http
      --http.addr="0.0.0.0"
      --http.port=8545
      --http.corsdomain="*"
      --http.vhosts="*"
      --validator.id <id>
      --validator.pubkey <pubkey>
      --validator.password /password
      --bootnodes <bootnodes>
      --monitor