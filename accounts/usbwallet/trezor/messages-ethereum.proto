// This file originates from the SatoshiLabs Trezor `common` repository at:
//   https://github.com/trezor/trezor-common/blob/master/protob/messages-ethereum.proto
// dated 28.05.2019, commit 893fd219d4a01bcffa0cd9cfa631856371ec5aa9.

syntax = "proto2";
package hw.trezor.messages.ethereum;

// Sugar for easier handling in Java
option java_package = "com.satoshilabs.trezor.lib.protobuf";
option java_outer_classname = "TrezorMessageEthereum";

import "messages-common.proto";


/**
 * Request: Ask device for public key corresponding to address_n path
 * @start
 * @next EthereumPublicKey
 * @next Failure
 */
message EthereumGetPublicKey {
    repeated uint32 address_n = 1;                                      // BIP-32 path to derive the key from master node
    optional bool show_display = 2;                                     // optionally show on display before sending the result
}

/**
 * Response: Contains public key derived from device private seed
 * @end
 */
message EthereumPublicKey {
    optional hw.trezor.messages.common.HDNodeType node = 1;        // BIP32 public node
    optional string xpub = 2;        // serialized form of public node
}

/**
 * Request: Ask device for Ethereum address corresponding to address_n path
 * @start
 * @next EthereumAddress
 * @next Failure
 */
message EthereumGetAddress {
    repeated uint32 address_n = 1;  // BIP-32 path to derive the key from master node
    optional bool show_display = 2; // optionally show on display before sending the result
}

/**
 * Response: Contains an Ethereum address derived from device private seed
 * @end
 */
message EthereumAddress {
    optional bytes  addressBin = 1;    // Ethereum address as 20 bytes (legacy firmwares)
    optional string addressHex = 2;    // Ethereum address as hex string (newer firmwares)
}

/**
 * Request: Ask device to sign transaction
 * All fields are optional from the protocol's point of view. Each field defaults to value `0` if missing.
 * Note: the first at most 1024 bytes of data MUST be transmitted as part of this message.
 * @start
 * @next EthereumTxRequest
 * @next Failure
 */
message EthereumSignTx {
    repeated uint32 address_n = 1;          // BIP-32 path to derive the key from master node
    optional bytes nonce = 2;               // <=256 bit unsigned big endian
    optional bytes gas_price = 3;           // <=256 bit unsigned big endian (in wei)
    optional bytes gas_limit = 4;           // <=256 bit unsigned big endian
    optional bytes toBin = 5;               // recipient address (20 bytes, legacy firmware)
    optional string toHex = 11;             // recipient address (hex string, newer firmware)
    optional bytes value = 6;               // <=256 bit unsigned big endian (in wei)
    optional bytes data_initial_chunk = 7;  // The initial data chunk (<= 1024 bytes)
    optional uint32 data_length = 8;        // Length of transaction payload
    optional uint32 chain_id = 9;           // Chain Id for EIP 155
    optional uint32 tx_type = 10;           // (only for Wanchain)
}

/**
 * Response: Device asks for more data from transaction payload, or returns the signature.
 * If data_length is set, device awaits that many more bytes of payload.
 * Otherwise, the signature_* fields contain the computed transaction signature. All three fields will be present.
 * @end
 * @next EthereumTxAck
 */
message EthereumTxRequest {
    optional uint32 data_length = 1;    // Number of bytes being requested (<= 1024)
    optional uint32 signature_v = 2;    // Computed signature (recovery parameter, limited to 27 or 28)
    optional bytes signature_r = 3;     // Computed signature R component (256 bit)
    optional bytes signature_s = 4;     // Computed signature S component (256 bit)
}

/**
 * Request: Transaction payload data.
 * @next EthereumTxRequest
 */
message EthereumTxAck {
    optional bytes data_chunk = 1;  // Bytes from transaction payload (<= 1024 bytes)
}

/**
 * Request: Ask device to sign message
 * @start
 * @next EthereumMessageSignature
 * @next Failure
 */
message EthereumSignMessage {
    repeated uint32 address_n = 1;  // BIP-32 path to derive the key from master node
    optional bytes message = 2;     // message to be signed
}

/**
 * Response: Signed message
 * @end
 */
message EthereumMessageSignature {
    optional bytes addressBin = 1;     // address used to sign the message (20 bytes, legacy firmware)
    optional bytes signature = 2;      // signature of the message
    optional string addressHex = 3;    // address used to sign the message (hex string, newer firmware)
}

/**
 * Request: Ask device to verify message
 * @start
 * @next Success
 * @next Failure
 */
message EthereumVerifyMessage {
    optional bytes addressBin = 1;  // address to verify (20 bytes, legacy firmware)
    optional bytes signature = 2;   // signature to verify
    optional bytes message = 3;     // message to verify
    optional string addressHex = 4; // address to verify (hex string, newer firmware)
}
