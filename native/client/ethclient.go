package native

import (
	"context"

	"github.com/unicornultrafoundation/go-u2u/ethclient"
	"github.com/unicornultrafoundation/go-u2u/rpc"
)

// Client extends Ethereum API client with typed wrappers for the Backend API.
type Client struct {
	ethclient.Client
	c *rpc.Client
}

// <PERSON>al connects a client to the given URL.
func Dial(rawurl string) (*Client, error) {
	return DialContext(context.Background(), rawurl)
}

func DialContext(ctx context.Context, rawurl string) (*Client, error) {
	c, err := rpc.DialContext(ctx, rawurl)
	if err != nil {
		return nil, err
	}
	return NewClient(c), nil
}

// NewClient creates a client that uses the given RPC client.
func NewClient(c *rpc.Client) *Client {
	return &Client{
		Client: *ethclient.NewClient(c),
		c:      c,
	}
}
